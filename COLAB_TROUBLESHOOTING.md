# 🔧 OmniGen2 Colab Troubleshooting Guide

## 🚨 Model Download Issues (Most Common)

### Problem: Step 4 gets stuck on "Cloning into 'pretrained_models/OmniGen2'..."

**Quick Fix:**
1. **Stop the cell** (click the stop button ⏹️)
2. **Skip to Step 4c** (Emergency Download Fix)
3. Run the emergency download cell instead

**Why this happens:**
- Large model files (8-12 GB) can timeout on slow connections
- Git LFS downloads can be unreliable in Colab
- Network interruptions cause the download to hang

**Best Solution:**
The emergency download method only downloads essential config files (~1 MB) and lets the model auto-download the rest when first used. This is actually **faster and more reliable**!

---

## 🔋 GPU Runtime Issues

### Problem: "No GPU available" error

**Solution:**
1. Go to `Runtime` → `Change runtime type`
2. Select `Hardware accelerator: GPU`
3. Choose `GPU type: T4` (or L4/A100 if available)
4. Click `Save`
5. Restart and run from the beginning

### Problem: "CUDA out of memory" error

**Solutions:**
1. **Reduce image size:**
   ```python
   HEIGHT = 512  # Instead of 1024
   WIDTH = 512   # Instead of 1024
   ```

2. **Reduce inference steps:**
   ```python
   INFERENCE_STEPS = 25  # Instead of 50
   ```

3. **Restart runtime:**
   - `Runtime` → `Restart runtime`
   - Run cells again

---

## 📦 Package Installation Issues

### Problem: Import errors or missing packages

**Solution:**
```python
# Run this in a new cell
!pip install --upgrade torch torchvision transformers diffusers accelerate
!pip install --upgrade huggingface_hub
```

### Problem: "Cannot import FusedRMSNorm" warnings

**Solution:**
These warnings are normal and don't affect functionality. The model will use a fallback implementation.

---

## 🖼️ Image Generation Issues

### Problem: Generation takes too long or fails

**Solutions:**
1. **Check GPU is being used:**
   ```python
   import torch
   print(f"GPU available: {torch.cuda.is_available()}")
   print(f"GPU name: {torch.cuda.get_device_name(0) if torch.cuda.is_available() else 'None'}")
   ```

2. **Reduce parameters:**
   - Lower resolution (512x512 instead of 1024x1024)
   - Fewer inference steps (25-30 instead of 50)
   - Simpler prompts

3. **Restart if needed:**
   - `Runtime` → `Restart runtime`
   - Run essential cells only

### Problem: Generated image is blank or corrupted

**Solutions:**
1. **Check model download:**
   ```python
   import os
   model_path = "pretrained_models/OmniGen2"
   print(f"Config exists: {os.path.exists(f'{model_path}/config.json')}")
   ```

2. **Try different prompt:**
   - Use simpler, more descriptive prompts
   - Avoid very long or complex instructions

3. **Adjust guidance scale:**
   ```python
   GUIDANCE_SCALE = 7.0  # Try higher values (3.0-10.0)
   ```

---

## 🌐 Network and Connection Issues

### Problem: Downloads fail or timeout

**Solutions:**
1. **Check internet connection**
2. **Use emergency download method** (Step 4c)
3. **Try at different times** (HuggingFace servers can be busy)

### Problem: "Repository not found" errors

**Solution:**
The repository URL might have changed. Try:
```python
# Alternative repository (if main one is unavailable)
model_repo = "OmniGen2/OmniGen2"  # Verify this is correct
```

---

## 🔄 Runtime and Session Issues

### Problem: Session disconnected or runtime crashed

**Solutions:**
1. **Reconnect:** Click "Reconnect" when prompted
2. **Restart runtime:** `Runtime` → `Restart runtime`
3. **Run cells in order:** Start from Step 1
4. **Use emergency download:** Skip to Step 4c for faster setup

### Problem: "Disk space full" error

**Solution:**
```python
# Clean up space
!rm -rf /tmp/*
!rm -rf ~/.cache/huggingface/
!df -h  # Check available space
```

---

## 💡 Performance Optimization Tips

### For Faster Setup:
1. **Use emergency download method** (Step 4c) - much faster
2. **Skip full model download** - let it auto-download as needed
3. **Use smaller image sizes** for testing

### For Better Results:
1. **Use descriptive prompts:** Include style, lighting, composition details
2. **Optimal settings:**
   - Steps: 30-50
   - Guidance: 4.0-7.0
   - Size: 1024x1024 for best quality
3. **Experiment with seeds** for reproducible results

### For Colab Free Tier:
1. **Use 512x512 images** to save memory
2. **Generate one image at a time**
3. **Close other browser tabs** to save RAM

---

## 🆘 Last Resort Solutions

### If Nothing Works:
1. **Fresh start:**
   - `Runtime` → `Restart runtime`
   - `Runtime` → `Factory reset runtime`
   - Start over with Step 1

2. **Try different browser:**
   - Chrome usually works best
   - Disable ad blockers
   - Clear browser cache

3. **Check Colab status:**
   - Visit [Colab status page](https://status.cloud.google.com/)
   - Try again later if there are known issues

4. **Alternative approach:**
   - Use the local installation instead
   - Try a different time when servers are less busy

---

## 📞 Getting Help

If you're still having issues:

1. **Check the error message carefully** - it usually tells you what's wrong
2. **Try the emergency download method** - solves 90% of issues
3. **Restart runtime and try again** - fixes most memory/session issues
4. **Use smaller parameters** - reduces memory usage

**Common Error Patterns:**
- `git clone` hanging → Use emergency download
- `CUDA out of memory` → Reduce image size/steps
- `Import errors` → Restart runtime and reinstall packages
- `No GPU` → Change runtime type to GPU

Remember: The emergency download method (Step 4c) is often the best solution and is actually faster than the full download!
