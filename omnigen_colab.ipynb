{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 🎨 OmniGen2 on Google Colab\n", "\n", "This notebook allows you to run the OmniGen2 text-to-image generation model on Google Colab with GPU acceleration.\n", "\n", "**⚠️ Important Setup Instructions:**\n", "1. **Enable GPU Runtime**: Go to `Runtime` → `Change runtime type` → Select `GPU` (T4, L4, or A100)\n", "2. **Check GPU Availability**: Run the first cell to verify GPU is available\n", "3. **Estimated Runtime**: Initial setup takes ~5-10 minutes, generation takes ~30 seconds per image\n", "\n", "---"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🔧 Step 1: Environment Setup & GPU Check\n", "\n", "First, let's verify that GPU is available and set up the environment."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import torch\n", "import os\n", "import subprocess\n", "import sys\n", "\n", "# Check GPU availability\n", "if torch.cuda.is_available():\n", "    gpu_name = torch.cuda.get_device_name(0)\n", "    gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3\n", "    print(f\"✅ GPU Available: {gpu_name}\")\n", "    print(f\"📊 GPU Memory: {gpu_memory:.1f} GB\")\n", "    print(f\"🔥 CUDA Version: {torch.version.cuda}\")\n", "else:\n", "    print(\"❌ No GPU available!\")\n", "    print(\"Please enable GPU runtime: Runtime → Change runtime type → GPU\")\n", "    sys.exit(1)\n", "\n", "# Check if we're already in the OmniGen2 directory\n", "if os.path.exists('omnigen2') and os.path.exists('inference.py'):\n", "    print(\"✅ Already in OmniGen2 directory\")\n", "else:\n", "    print(\"📁 Need to clone repository...\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 📥 Step 2: Clone Repository (if needed)\n", "\n", "Clone the OmniGen2 repository if we're not already in it."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "\n", "# Only clone if we're not already in the directory\n", "if not (os.path.exists('omnigen2') and os.path.exists('inference.py')):\n", "    print(\"🔄 Cloning OmniGen2 repository...\")\n", "    !git clone https://github.com/VectorSpaceLab/OmniGen2.git\n", "    %cd OmniGen2\n", "    print(\"✅ Repository cloned successfully!\")\n", "else:\n", "    print(\"✅ Already in OmniGen2 directory, skipping clone\")\n", "\n", "# Verify we have the necessary files\n", "required_files = ['inference.py', 'requirements.txt', 'omnigen2']\n", "missing_files = [f for f in required_files if not os.path.exists(f)]\n", "\n", "if missing_files:\n", "    print(f\"❌ Missing required files: {missing_files}\")\n", "    print(\"Please check the repository structure\")\n", "else:\n", "    print(\"✅ All required files found\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 📦 Step 3: Install Dependencies\n", "\n", "Install all required Python packages. This may take a few minutes."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"📦 Installing dependencies...\")\n", "print(\"This may take 3-5 minutes...\")\n", "\n", "# Install requirements with progress\n", "!pip install -r requirements.txt --progress-bar on\n", "\n", "# Install additional Colab-specific dependencies if needed\n", "try:\n", "    import accelerate\n", "    import diffusers\n", "    import transformers\n", "    print(\"✅ Core dependencies installed successfully!\")\n", "except ImportError as e:\n", "    print(f\"❌ Import error: {e}\")\n", "    print(\"Trying to install missing packages...\")\n", "    !pip install accelerate diffusers transformers\n", "\n", "print(\"✅ Dependencies installation complete!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🤗 Step 4: Download Pre-trained Models\n", "\n", "Download the pre-trained models from Hugging Face. This is the largest download (~8-12 GB)."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "from pathlib import Path\n", "import time\n", "\n", "model_path = \"pretrained_models/OmniGen2\"\n", "\n", "# Check if models are already downloaded\n", "if os.path.exists(model_path) and os.path.exists(f\"{model_path}/config.json\"):\n", "    print(\"✅ Models already downloaded!\")\n", "    # Verify model files\n", "    model_files = list(Path(model_path).rglob(\"*.safetensors\"))\n", "    if len(model_files) > 0:\n", "        total_size = sum(f.stat().st_size for f in model_files) / (1024**3)\n", "        print(f\"📊 Model size: {total_size:.1f} GB\")\n", "        print(f\"📁 Found {len(model_files)} model files\")\n", "    else:\n", "        print(\"⚠️ Model files seem incomplete, re-downloading...\")\n", "        !rm -rf {model_path}\n", "else:\n", "    print(\"📥 Downloading models from Hugging Face...\")\n", "    print(\"⏱️ This will take 5-10 minutes depending on connection speed\")\n", "    print(\"🔄 Using optimized download method...\")\n", "    \n", "    # First, try using huggingface_hub for faster downloads\n", "    try:\n", "        print(\"📦 Installing huggingface_hub for optimized downloads...\")\n", "        !pip install -q huggingface_hub\n", "        \n", "        from huggingface_hub import snapshot_download\n", "        import threading\n", "        import sys\n", "        \n", "        # Progress indicator\n", "        def show_progress():\n", "            chars = \"⠋⠙⠹⠸⠼⠴⠦⠧⠇⠏\"\n", "            i = 0\n", "            while not download_complete:\n", "                print(f\"\\r{chars[i % len(chars)]} Downloading models...\", end=\"\", flush=True)\n", "                time.sleep(0.1)\n", "                i += 1\n", "        \n", "        download_complete = False\n", "        progress_thread = threading.Thread(target=show_progress)\n", "        progress_thread.daemon = True\n", "        progress_thread.start()\n", "        \n", "        print(\"🚀 Starting optimized download...\")\n", "        snapshot_download(\n", "            repo_id=\"OmniGen2/OmniGen2\",\n", "            local_dir=model_path,\n", "            local_dir_use_symlinks=False,\n", "            resume_download=True\n", "        )\n", "        \n", "        download_complete = True\n", "        print(\"\\r✅ Optimized download completed!\" + \" \" * 20)\n", "        \n", "    except Exception as e:\n", "        print(f\"\\r⚠️ Optimized download failed: {e}\" + \" \" * 20)\n", "        print(\"🔄 Falling back to git clone method...\")\n", "        \n", "        # Fallback to git clone with timeout\n", "        !sudo apt-get update -qq\n", "        !sudo apt-get install -y git-lfs\n", "        !git lfs install\n", "        \n", "        # Create directory\n", "        !mkdir -p pretrained_models\n", "        \n", "        # Use git clone with progress\n", "        print(\"📥 Cloning repository (this may take several minutes)...\")\n", "        !timeout 600 git clone --progress https://huggingface.co/OmniGen2/OmniGen2 {model_path} || echo \"Download timed out or failed\"\n", "    \n", "    # Verify download\n", "    if os.path.exists(f\"{model_path}/config.json\"):\n", "        print(\"✅ Models downloaded successfully!\")\n", "        model_files = list(Path(model_path).rglob(\"*.safetensors\"))\n", "        if len(model_files) > 0:\n", "            total_size = sum(f.stat().st_size for f in model_files) / (1024**3)\n", "            print(f\"📊 Model size: {total_size:.1f} GB\")\n", "            print(f\"📁 Found {len(model_files)} model files\")\n", "        else:\n", "            print(\"⚠️ Model files may still be downloading...\")\n", "    else:\n", "        print(\"❌ Model download failed!\")\n", "        print(\"🔧 Troubleshooting options:\")\n", "        print(\"1. Check internet connection\")\n", "        print(\"2. Try running this cell again\")\n", "        print(\"3. <PERSON><PERSON> runtime and try again\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🔧 Step 4b: Alternative Download Method (if Step 4 failed)\n", "\n", "If the download in Step 4 got stuck or failed, try this alternative method:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Alternative download method - only run if Step 4 failed\n", "import os\n", "import subprocess\n", "import time\n", "\n", "model_path = \"pretrained_models/OmniGen2\"\n", "\n", "# Check if we need to download\n", "if not (os.path.exists(model_path) and os.path.exists(f\"{model_path}/config.json\")):\n", "    print(\"🔄 Trying alternative download method...\")\n", "    \n", "    # Clean up any partial downloads\n", "    !rm -rf {model_path}\n", "    !mkdir -p pretrained_models\n", "    \n", "    # Method 1: Direct file download using wget\n", "    try:\n", "        print(\"📥 Method 1: Direct file download...\")\n", "        \n", "        # Create model directory\n", "        os.makedirs(model_path, exist_ok=True)\n", "        \n", "        # Download essential files first\n", "        base_url = \"https://huggingface.co/OmniGen2/OmniGen2/resolve/main\"\n", "        essential_files = [\n", "            \"config.json\",\n", "            \"model_index.json\",\n", "            \"README.md\"\n", "        ]\n", "        \n", "        for file in essential_files:\n", "            print(f\"📄 Downloading {file}...\")\n", "            !wget -q \"{base_url}/{file}\" -O \"{model_path}/{file}\"\n", "        \n", "        # Check if config downloaded successfully\n", "        if os.path.exists(f\"{model_path}/config.json\"):\n", "            print(\"✅ Essential files downloaded!\")\n", "            print(\"⚠️ Note: Large model files will be downloaded automatically when needed\")\n", "        else:\n", "            raise Exception(\"Failed to download essential files\")\n", "            \n", "    except Exception as e:\n", "        print(f\"❌ Alternative method failed: {e}\")\n", "        print(\"\\n🆘 Manual solution:\")\n", "        print(\"1. Go to https://huggingface.co/OmniGen2/OmniGen2\")\n", "        print(\"2. Click 'Files and versions' tab\")\n", "        print(\"3. Download config.json manually\")\n", "        print(\"4. Upload it to the pretrained_models/OmniGen2/ folder\")\n", "        \n", "else:\n", "    print(\"✅ Models already available, skipping alternative download\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🎨 Step 5: Interactive Text-to-Image Generation\n", "\n", "Now let's set up an interactive interface for generating images with custom prompts!"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Verify model availability before generation\n", "import os\n", "from pathlib import Path\n", "\n", "model_path = \"pretrained_models/OmniGen2\"\n", "\n", "print(\"🔍 Verifying model files...\")\n", "\n", "# Check essential files\n", "essential_files = [\"config.json\", \"model_index.json\"]\n", "missing_files = [f for f in essential_files if not os.path.exists(f\"{model_path}/{f}\")]\n", "\n", "if missing_files:\n", "    print(f\"❌ Missing essential files: {missing_files}\")\n", "    print(\"Please complete Step 4 (model download) first!\")\n", "else:\n", "    print(\"✅ Essential model files found\")\n", "    \n", "    # Check for model weights\n", "    model_files = list(Path(model_path).rglob(\"*.safetensors\"))\n", "    if len(model_files) > 0:\n", "        total_size = sum(f.stat().st_size for f in model_files) / (1024**3)\n", "        print(f\"📊 Found {len(model_files)} model files ({total_size:.1f} GB)\")\n", "    else:\n", "        print(\"⚠️ No model weight files found - they will be downloaded automatically during generation\")\n", "    \n", "    # Create output directory\n", "    os.makedirs(\"outputs\", exist_ok=True)\n", "    print(\"✅ Ready for generation!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Interactive parameters - modify these as needed!\n", "PROMPT = \"The sun rises slightly, the dew on the rose petals in the garden is clear, a crystal ladybug is crawling to the dew, the background is the early morning garden, macro lens.\"\n", "HEIGHT = 1024\n", "WIDTH = 1024\n", "INFERENCE_STEPS = 50\n", "GUIDANCE_SCALE = 4.0\n", "NUM_IMAGES = 1\n", "SEED = 42  # Set to -1 for random seed\n", "\n", "print(\"🎨 Generation Parameters:\")\n", "print(f\"📝 Prompt: {PROMPT}\")\n", "print(f\"📐 Size: {WIDTH}x{HEIGHT}\")\n", "print(f\"🔢 Steps: {INFERENCE_STEPS}\")\n", "print(f\"🎯 Guidance: {GUIDANCE_SCALE}\")\n", "print(f\"🖼️ Images: {NUM_IMAGES}\")\n", "print(f\"🎲 Seed: {SEED}\")\n", "print(\"\\n🚀 Starting generation...\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import time\n", "import subprocess\n", "\n", "# Build the command\n", "cmd = [\n", "    \"python\", \"inference.py\",\n", "    \"--model_path\", \"pretrained_models/OmniGen2\",\n", "    \"--num_inference_step\", str(INFERENCE_STEPS),\n", "    \"--height\", str(HEIGHT),\n", "    \"--width\", str(WIDTH),\n", "    \"--text_guidance_scale\", str(GUIDANCE_SCALE),\n", "    \"--instruction\", PROMPT,\n", "    \"--output_image_path\", \"outputs/generated_image.png\",\n", "    \"--num_images_per_prompt\", str(NUM_IMAGES)\n", "]\n", "\n", "if SEED != -1:\n", "    cmd.extend([\"--seed\", str(SEED)])\n", "\n", "# Run the generation\n", "start_time = time.time()\n", "try:\n", "    result = subprocess.run(cmd, capture_output=True, text=True, check=True)\n", "    end_time = time.time()\n", "    \n", "    print(f\"✅ Generation completed in {end_time - start_time:.1f} seconds!\")\n", "    if result.stdout:\n", "        print(\"📄 Output:\", result.stdout)\n", "        \n", "except subprocess.CalledProcessError as e:\n", "    print(f\"❌ Generation failed: {e}\")\n", "    print(f\"📄 Error output: {e.stderr}\")\n", "    print(f\"📄 Standard output: {e.stdout}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🖼️ Step 6: Display Generated Images\n", "\n", "Let's display the generated image(s) with some additional information."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "from IPython.display import Image, display\n", "from PIL import Image as PILImage\n", "import matplotlib.pyplot as plt\n", "\n", "# Check if the image was generated\n", "image_path = \"outputs/generated_image.png\"\n", "\n", "if os.path.exists(image_path):\n", "    print(\"🎉 Image generated successfully!\")\n", "    \n", "    # Get image info\n", "    with PILImage.open(image_path) as img:\n", "        width, height = img.size\n", "        file_size = os.path.getsize(image_path) / (1024 * 1024)  # MB\n", "    \n", "    print(f\"📐 Dimensions: {width}x{height}\")\n", "    print(f\"📁 File size: {file_size:.1f} MB\")\n", "    print(f\"📝 Prompt used: {PROMPT}\")\n", "    print(\"\\n🖼️ Generated Image:\")\n", "    \n", "    # Display the image\n", "    display(Image(image_path))\n", "    \n", "    # Also create a matplotlib version for better control\n", "    plt.figure(figsize=(10, 10))\n", "    img = PILImage.open(image_path)\n", "    plt.imshow(img)\n", "    plt.axis('off')\n", "    plt.title(f\"Generated Image\\n{PROMPT[:60]}{'...' if len(PROMPT) > 60 else ''}\", \n", "              fontsize=12, pad=20)\n", "    plt.tight_layout()\n", "    plt.show()\n", "    \n", "else:\n", "    print(\"❌ No image found! Generation may have failed.\")\n", "    print(\"Please check the generation step above for errors.\")\n", "    \n", "    # List files in outputs directory for debugging\n", "    if os.path.exists(\"outputs\"):\n", "        files = os.listdir(\"outputs\")\n", "        print(f\"📁 Files in outputs directory: {files}\")\n", "    else:\n", "        print(\"📁 Outputs directory doesn't exist\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🔄 Step 7: Generate More Images (Optional)\n", "\n", "Want to generate more images? Modify the parameters below and run the cells!"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 🎨 Try different prompts!\n", "sample_prompts = [\n", "    \"A majestic dragon flying over a medieval castle at sunset, fantasy art style\",\n", "    \"A cute robot cat sitting in a field of colorful flowers, digital art\",\n", "    \"A futuristic city with flying cars and neon lights, cyberpunk style\",\n", "    \"A peaceful zen garden with cherry blossoms and a small pond\",\n", "    \"A magical forest with glowing mushrooms and fairy lights\"\n", "]\n", "\n", "print(\"🎨 Sample prompts you can try:\")\n", "for i, prompt in enumerate(sample_prompts, 1):\n", "    print(f\"{i}. {prompt}\")\n", "\n", "print(\"\\n💡 Tips for better results:\")\n", "print(\"• Be specific about style (e.g., 'digital art', 'oil painting', 'photorealistic')\")\n", "print(\"• Include lighting details (e.g., 'golden hour', 'dramatic lighting')\")\n", "print(\"• Specify composition (e.g., 'close-up', 'wide shot', 'macro lens')\")\n", "print(\"• Add quality modifiers (e.g., 'highly detailed', '4K', 'masterpiece')\")\n", "\n", "print(\"\\n⚙️ Parameter recommendations:\")\n", "print(\"• Steps: 30-50 (higher = better quality, slower)\")\n", "print(\"• Guidance: 3.0-7.0 (higher = follows prompt more closely)\")\n", "print(\"• Size: 512x512 (fast), 1024x1024 (high quality)\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 💾 Step 8: Download Your Images\n", "\n", "Download the generated images to your local machine."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import zipfile\n", "from google.colab import files\n", "import glob\n", "\n", "# Create a zip file with all generated images\n", "output_files = glob.glob(\"outputs/*.png\") + glob.glob(\"outputs/*.jpg\")\n", "\n", "if output_files:\n", "    zip_filename = \"omnigen2_generated_images.zip\"\n", "    \n", "    with zipfile.ZipFile(zip_filename, 'w') as zipf:\n", "        for file in output_files:\n", "            zipf.write(file, os.path.basename(file))\n", "    \n", "    print(f\"📦 Created zip file with {len(output_files)} images\")\n", "    print(f\"📁 Zip file: {zip_filename}\")\n", "    \n", "    # Download the zip file\n", "    files.download(zip_filename)\n", "    print(\"✅ Download started!\")\n", "    \n", "else:\n", "    print(\"❌ No images found to download\")\n", "    print(\"Please generate some images first!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["---\n", "\n", "## 🎉 Congratulations!\n", "\n", "You've successfully set up and used OmniGen2 on Google Colab! \n", "\n", "### 🔗 Useful Links:\n", "- [OmniGen2 GitHub Repository](https://github.com/VectorSpaceLab/OmniGen2)\n", "- [Hugging Face Model Page](https://huggingface.co/OmniGen2/OmniGen2)\n", "\n", "### 🐛 Troubleshooting:\n", "- **Out of memory**: Reduce image size or batch size\n", "- **Slow generation**: Make sure GPU runtime is enabled\n", "- **Model download fails**: Check internet connection and try again\n", "\n", "### 💡 Next Steps:\n", "- Experiment with different prompts and parameters\n", "- Try image-to-image generation by providing input images\n", "- Explore advanced features like negative prompts\n", "\n", "Happy generating! 🎨✨"]}], "metadata": {"accelerator": "GPU", "colab": {"gpuType": "T4", "provenance": [], "collapsed_sections": []}, "kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.12"}}, "nbformat": 4, "nbformat_minor": 4}