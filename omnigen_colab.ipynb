{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# OmniGen2 on Google Colab\n", "\n", "This notebook allows you to run the OmniGen2 text-to-image generation script on Google Colab, taking advantage of the available GPU."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. <PERSON><PERSON> the Repository\n", "\n", "First, we need to clone the GitHub repository to get all the necessary files."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["!git clone https://github.com/VectorSpaceLab/OmniGen2.git\n", "%cd OmniGen2"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Install Dependencies\n", "\n", "Next, we install all the required Python packages listed in `requirements.txt`."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["!pip install -r requirements.txt"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Download Pre-trained Models\n", "\n", "Download the pre-trained models from Hugging Face."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["!sudo apt-get install git-lfs\n", "!git lfs install\n", "!git clone https://huggingface.co/OmniGen2/OmniGen2 pretrained_models/OmniGen2"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Run Text-to-Image Inference\n", "\n", "Now we can run the inference script to generate an image from a text prompt. You can modify the `--instruction` to generate different images."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["!python inference.py \\\n", "  --model_path \"pretrained_models/OmniGen2\" \\\n", "  --num_inference_step 50 \\\n", "  --height 1024 \\\n", "  --width 1024 \\\n", "  --text_guidance_scale 4.0 \\\n", "  --instruction \"The sun rises slightly, the dew on the rose petals in the garden is clear, a crystal ladybug is crawling to the dew, the background is the early morning garden, macro lens.\" \\\n", "  --output_image_path \"outputs/output_t2i.png\" \\\n", "  --num_images_per_prompt 1"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Display the Generated Image\n", "\n", "Finally, let's display the image that was generated."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from IPython.display import Image\n", "Image('outputs/output_t2i.png')"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.9"}}, "nbformat": 4, "nbformat_minor": 4}