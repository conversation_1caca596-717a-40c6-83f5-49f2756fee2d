#!/usr/bin/env python3
"""
Test script to validate the OmniGen2 Colab notebook conversion.
This script checks if the notebook is properly structured and contains all necessary components.
"""

import json
import os
import sys
from pathlib import Path

def load_notebook(notebook_path):
    """Load and parse the <PERSON><PERSON><PERSON> notebook."""
    try:
        with open(notebook_path, 'r', encoding='utf-8') as f:
            notebook = json.load(f)
        return notebook
    except Exception as e:
        print(f"❌ Error loading notebook: {e}")
        return None

def validate_notebook_structure(notebook):
    """Validate the basic structure of the notebook."""
    issues = []
    
    # Check basic structure
    if 'cells' not in notebook:
        issues.append("Missing 'cells' key")
    
    if 'metadata' not in notebook:
        issues.append("Missing 'metadata' key")
    
    # Check metadata for Colab compatibility
    metadata = notebook.get('metadata', {})
    if 'accelerator' not in metadata:
        issues.append("Missing GPU accelerator metadata")
    
    if 'colab' not in metadata:
        issues.append("Missing Colab-specific metadata")
    
    return issues

def validate_cells(cells):
    """Validate the notebook cells."""
    issues = []
    cell_types = [cell.get('cell_type') for cell in cells]
    
    # Check for required cell types
    if 'markdown' not in cell_types:
        issues.append("No markdown cells found")
    
    if 'code' not in cell_types:
        issues.append("No code cells found")
    
    # Check for essential components
    code_content = []
    for cell in cells:
        if cell.get('cell_type') == 'code':
            source = cell.get('source', [])
            if isinstance(source, list):
                code_content.extend(source)
            else:
                code_content.append(source)
    
    full_code = '\n'.join(code_content)
    
    # Check for essential imports and functionality
    essential_checks = [
        ('torch', 'PyTorch import'),
        ('cuda.is_available', 'GPU availability check'),
        ('git clone', 'Repository cloning'),
        ('pip install', 'Dependency installation'),
        ('inference.py', 'Inference script usage'),
        ('IPython.display', 'Image display functionality'),
    ]
    
    for check, description in essential_checks:
        if check not in full_code:
            issues.append(f"Missing {description} ({check})")
    
    return issues

def validate_colab_features(cells):
    """Validate Colab-specific features."""
    issues = []
    code_content = []
    
    for cell in cells:
        if cell.get('cell_type') == 'code':
            source = cell.get('source', [])
            if isinstance(source, list):
                code_content.extend(source)
            else:
                code_content.append(source)
    
    full_code = '\n'.join(code_content)
    
    # Check for Colab-specific features
    colab_features = [
        ('!git', 'Shell commands'),
        ('!pip', 'Package installation'),
        ('!sudo', 'System commands'),
        ('%cd', 'Directory change magic command'),
        ('files.download', 'File download functionality'),
    ]
    
    for feature, description in colab_features:
        if feature not in full_code:
            issues.append(f"Missing {description} ({feature})")
    
    return issues

def check_user_experience(cells):
    """Check for good user experience elements."""
    suggestions = []
    markdown_content = []
    
    for cell in cells:
        if cell.get('cell_type') == 'markdown':
            source = cell.get('source', [])
            if isinstance(source, list):
                markdown_content.extend(source)
            else:
                markdown_content.append(source)
    
    full_markdown = '\n'.join(markdown_content)
    
    # Check for user-friendly elements
    ux_elements = [
        ('GPU', 'GPU setup instructions'),
        ('Step', 'Step-by-step structure'),
        ('⚠️', 'Warning symbols'),
        ('✅', 'Success indicators'),
        ('❌', 'Error indicators'),
        ('Tips', 'User tips'),
        ('Troubleshooting', 'Troubleshooting section'),
    ]
    
    for element, description in ux_elements:
        if element not in full_markdown:
            suggestions.append(f"Consider adding {description}")
    
    return suggestions

def main():
    """Main validation function."""
    notebook_path = "omnigen_colab.ipynb"
    
    if not os.path.exists(notebook_path):
        print(f"❌ Notebook not found: {notebook_path}")
        return False
    
    print(f"🔍 Validating notebook: {notebook_path}")
    
    # Load notebook
    notebook = load_notebook(notebook_path)
    if not notebook:
        return False
    
    # Validate structure
    print("\n📋 Checking notebook structure...")
    structure_issues = validate_notebook_structure(notebook)
    if structure_issues:
        print("❌ Structure issues found:")
        for issue in structure_issues:
            print(f"  • {issue}")
    else:
        print("✅ Notebook structure is valid")
    
    # Validate cells
    print("\n🔬 Checking notebook cells...")
    cells = notebook.get('cells', [])
    cell_issues = validate_cells(cells)
    if cell_issues:
        print("❌ Cell issues found:")
        for issue in cell_issues:
            print(f"  • {issue}")
    else:
        print("✅ Notebook cells are valid")
    
    # Validate Colab features
    print("\n🌐 Checking Colab compatibility...")
    colab_issues = validate_colab_features(cells)
    if colab_issues:
        print("❌ Colab compatibility issues:")
        for issue in colab_issues:
            print(f"  • {issue}")
    else:
        print("✅ Colab features are present")
    
    # Check user experience
    print("\n👤 Checking user experience...")
    ux_suggestions = check_user_experience(cells)
    if ux_suggestions:
        print("💡 User experience suggestions:")
        for suggestion in ux_suggestions:
            print(f"  • {suggestion}")
    else:
        print("✅ Good user experience elements found")
    
    # Summary
    total_issues = len(structure_issues) + len(cell_issues) + len(colab_issues)
    print(f"\n📊 Validation Summary:")
    print(f"  • Total cells: {len(cells)}")
    print(f"  • Critical issues: {total_issues}")
    print(f"  • UX suggestions: {len(ux_suggestions)}")
    
    if total_issues == 0:
        print("\n🎉 Notebook validation passed! Ready for Colab.")
        return True
    else:
        print(f"\n⚠️ Found {total_issues} issues that should be addressed.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
